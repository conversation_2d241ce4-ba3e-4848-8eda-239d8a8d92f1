"""
WebSocket handler for storage operations.
Handles storage pickup using pickup_process directly and storage insertion using select_sections from process_manager.
"""

import json
import logging
import asyncio
from fastapi import WebSocket, WebSocketDisconnect
from managers import ws_manager, session_manager
from infrastructure.repositories.storage_repository import StorageRepository

logger = logging.getLogger(__name__)

async def handle_storage_pickup_websocket(websocket: WebSocket, session_id: str):
    """
    Handle WebSocket connection for storage pickup operations.
    Uses pickup_process directly as defined in screen_communication.md

    Args:
        websocket: FastAPI WebSocket connection
        session_id: Session ID for this connection
    """
    logger.info(f"Storage pickup WebSocket handler started for session: {session_id}")

    try:
        # Register WebSocket connection
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Storage pickup WebSocket connected: {session_id}")

        # Get session data
        session = session_manager.get_session(session_id)
        if not session:
            logger.error(f"No session found for {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Session not found"
            })
            return

        section_id = getattr(session, 'section_id', None)
        requires_payment = getattr(session, 'amount', 0) > 0
        reservation_id = getattr(session, 'reservation_id', None)

        if not section_id:
            logger.error(f"No section_id in session {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "No section ID found"
            })
            return

        # Create message queue for pickup_process
        message_queue = asyncio.Queue()

        # Message handler to route WebSocket messages to pickup_process
        async def handle_websocket_messages():
            while ws_manager.is_connected(session_id):
                try:
                    message = await websocket.receive_text()
                    if not message or message.strip() == "":
                        continue

                    try:
                        data = json.loads(message)
                    except json.JSONDecodeError:
                        await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                        continue

                    msg_type = data.get("type")

                    if msg_type == "ping":
                        await ws_manager.send(session_id, {"type": "pong"})
                        continue

                    # Route message to pickup_process
                    await message_queue.put(data)

                except WebSocketDisconnect:
                    logger.info(f"Storage pickup WebSocket disconnected: {session_id}")
                    break
                except Exception as e:
                    logger.error(f"Error processing WebSocket message: {e}")
                    break

        # Start message handler task
        message_task = asyncio.create_task(handle_websocket_messages())

        # Start pickup_process directly from universal process_manager
        from managers.process_manager import pickup_process
        success, successful_sections = await pickup_process(
            sections=[section_id],
            session_id=session_id,
            message_queue=message_queue,
            requires_payment=requires_payment
        )

        # Cancel message handler
        message_task.cancel()

        # Complete storage pickup operation
        if success and successful_sections and reservation_id:
            repo = StorageRepository()
            repo.deactivate_reservation(reservation_id)
            logger.info(f"Deactivated storage reservation {reservation_id} after pickup")

        logger.info(f"Storage pickup completed for session {session_id}: success={success}")

    except Exception as e:
        logger.error(f"Error in storage pickup WebSocket handler: {e}")
    finally:
        # Clean up
        ws_manager.disconnect(session_id)
        logger.info(f"Storage pickup WebSocket handler ended for session: {session_id}")


async def handle_storage_insert_websocket(websocket: WebSocket, session_id: str):
    """
    Handle WebSocket connection for storage insertion operations.
    Uses select_sections function from process_manager for door opening.

    Args:
        websocket: FastAPI WebSocket connection
        session_id: Session ID for this connection
    """
    logger.info(f"Storage insert WebSocket handler started for session: {session_id}")

    try:
        # Register WebSocket connection
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Storage insert WebSocket connected: {session_id}")

        # Get session data
        session = session_manager.get_session(session_id)
        if not session:
            logger.error(f"No session found for {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Session not found"
            })
            return

        section_id = getattr(session, 'section_id', None)
        if not section_id:
            logger.error(f"No section_id in session {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "No section ID found"
            })
            return

        # Create message queue for communication with select_sections
        message_queue = asyncio.Queue()

        # Message queue registration handled automatically by pickup_process() if payment required

        # Message handler to route WebSocket messages to select_sections
        async def handle_websocket_messages():
            while ws_manager.is_connected(session_id):
                try:
                    message = await websocket.receive_text()
                    if not message or message.strip() == "":
                        continue

                    try:
                        data = json.loads(message)
                    except json.JSONDecodeError:
                        await ws_manager.send(session_id, {"type": "error", "message": "Invalid JSON"})
                        continue

                    msg_type = data.get("type")

                    if msg_type == "ping":
                        await ws_manager.send(session_id, {"type": "pong"})
                        continue

                    # Route message to select_sections
                    await message_queue.put(data)

                except WebSocketDisconnect:
                    logger.info(f"Storage insert WebSocket disconnected: {session_id}")
                    break
                except Exception as e:
                    logger.error(f"Error processing WebSocket message: {e}")
                    break

        # Start message handler task
        message_task = asyncio.create_task(handle_websocket_messages())

        # Get session data for payment requirements
        requires_payment = getattr(session, 'amount', 0) > 0

        # PAYMENT STEP - Use universal pickup_process for payment handling
        if requires_payment:
            logger.info("Payment required, using universal pickup_process for payment")

            # Use universal pickup_process just for payment (with empty sections list)
            from managers.payment_manager import payment_process_with_callbacks
            payment_success = await payment_process_with_callbacks(
                session_id=session_id,
                amount=getattr(session, 'amount', 0),
                message_queue=message_queue
            )

            if not payment_success:
                logger.info("Payment failed or cancelled - ending insertion process")
                return

            logger.info("Payment completed successfully - proceeding to section selection")

        # CREATE RESERVATION
        from infrastructure.repositories.storage_repository import StorageRepository
        repo = StorageRepository()
        reservation_id = repo.create_reservation(
            section_id=section_id,
            price=getattr(session, 'amount', 0),
            size_category=getattr(session, 'size_category', 0),
            email=getattr(session, 'email', None)
            )
        logger.info(f"Created storage reservation {reservation_id} for section {section_id}")

        # OPEN SECTION
        from managers.process_manager import select_sections
        success, selected_sections = await select_sections(
            enabled_sections=[section_id],  # Only one section is enabled for storage insertion
            available_sections=[section_id],  # Only one section is available for storage insertion
            session_id=session_id,
            message_queue=message_queue,
            wait_for_stop=False  # Don't wait for stop_selection for storage insertion
        )

        # Cancel message handler
        message_task.cancel()

        # Complete storage insertion operation
        if success and selected_sections:
            # Update reservation status after successful insertion
            repo = StorageRepository()
            # The reservation creation and completion is handled by the service layer
            logger.info(f"Storage insertion completed successfully for section {section_id}")

        logger.info(f"Storage insertion completed for session {session_id}: success={success}")

    except Exception as e:
        logger.error(f"Error in storage insert WebSocket handler: {e}")
    finally:
        # Message queue cleanup handled automatically by pickup_process() if payment was required
        # Clean up WebSocket connection
        ws_manager.disconnect(session_id)
        logger.info(f"Storage insert WebSocket handler ended for session: {session_id}")




async def handle_storage_websocket(websocket: WebSocket, session_id: str):
    """
    Main WebSocket handler for storage operations.
    Routes to appropriate handler based on session operation type.
    """
    from managers.session_manager import session_manager

    session = session_manager.get_session(session_id)
    if not session:
        logger.error(f"No session found for {session_id}")
        await websocket.close(code=1000, reason="Session not found")
        return

    operation = getattr(session, 'operation', None)

    if operation == "storage_pickup":
        await handle_storage_pickup_websocket(websocket, session_id)
    elif operation == "storage_insert":
        await handle_storage_insert_websocket(websocket, session_id)
    else:
        # No other storage operations supported
        logger.error(f"Unknown storage operation: {operation}")
        await websocket.close(code=1000, reason="Unknown operation")

