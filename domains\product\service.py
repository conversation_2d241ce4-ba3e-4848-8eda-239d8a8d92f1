"""
Product Service - Business logic for product operations.
Handles product pickup, insertion, and management operations.
"""

import logging
from typing import Dict, Optional, Any
from uuid import uuid4
from decimal import Decimal

from managers.session_manager import session_manager, SessionType, SessionStatus
from managers.timeline_logger import log_timeline_event

logger = logging.getLogger(__name__)

class ProductService:
    """Service for product-related business operations"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def pickup_product(
        self, 
        section_id: Optional[int], 
        reservation_pin: Optional[str]
    ) -> Dict[str, Any]:
        """
        Initialize product pickup process.
        Creates transaction session and starts flow.
        
        Args:
            section_id: Section ID to search in (customer pickup without PIN - only for non-reserved products)
            reservation_pin: Reservation PIN to search by (customer pickup with PIN - required for reserved products)
            
        Returns:
            Dict with pickup response data
            
        Note:
            - If product is reserved (reserved = 1), PIN is required
            - If product is not reserved, it can be picked up by section_id only
        """
        self.logger.info(f"Product pickup requested - section_id: {section_id}, reservation_pin: {reservation_pin}")
        
        try:
            # Import repository
            from infrastructure.repositories import product_repository
            
            # Find product for pickup using repository
            product = await product_repository.find_product_for_pickup(section_id, reservation_pin)
            if not product:
                # Log timeline event for failed pickup attempt
                if reservation_pin:
                    log_timeline_event(
                        event_type="pin_entered",
                        event_result="order_not_found",
                        message="Product reserved Box not found",
                        entered_pin=reservation_pin,
                        mode="product"
                    )
                    raise ValueError(f"No reserved product found with PIN: {reservation_pin}")
                elif section_id:
                    # Check if there's a reserved product in this section
                    reserved_product = await product_repository.find_reserved_product_in_section(section_id)
                    if reserved_product:
                        log_timeline_event(
                            event_type="section_access",
                            event_result="reserved_product_requires_pin",
                            message=f"Product in section {section_id} is reserved and requires PIN",
                            section_id=section_id,
                            mode="product"
                        )
                        raise ValueError(f"Product in section {section_id} is reserved and requires PIN for pickup")
                    else:
                        log_timeline_event(
                            event_type="section_access",
                            event_result="product_not_found",
                            message=f"No available product found in section {section_id}",
                            section_id=section_id,
                            mode="product"
                        )
                        raise ValueError(f"No available product found in section {section_id}")
                else:
                    log_timeline_event(
                        event_type="pickup_attempt",
                        event_result="no_criteria_provided",
                        message="No product found for pickup with provided criteria",
                        mode="product"
                    )
                    raise ValueError("No product found for pickup with provided criteria")

            # Log successful product found
            if reservation_pin:
                log_timeline_event(
                    event_type="pin_entered",
                    event_result="section_found",
                    message="Product reserved Box found",
                    entered_pin=reservation_pin,
                    section_id=product['section_id'],
                    mode="product"
                )
            else:
                log_timeline_event(
                    event_type="section_access",
                    event_result="product_found",
                    message=f"Product found in section {section_id}",
                    section_id=section_id,
                    mode="product"
                )
            
            product_id = product['id']
            section_id = int(product['section_id'])
            product_uuid = product['uuid']
            price = float(product.get('price', 0))
            
            self.logger.info(f"Found product {product_id} for pickup")
            
            # Check if product requires payment
            requires_payment = price > 0
            self.logger.info(f"Product {product_id} requires payment: {requires_payment}")
            
            # Create session for product pickup using simple session type
            session_id = str(uuid4())

            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.PRODUCT_PICKUP,  # Simple session type like storage
                product_id=product_id,
                product_uuid=product_uuid,
                section_id=section_id,
                amount=price,
                operation="product_pickup"
            )

            if not session:
                raise Exception("Failed to create session")

            return {
                "success": True,
                "session_id": session_id,
                "status": "flow_started",
                "requires_payment": requires_payment,
                "amount": price,
                "message": "Product pickup flow has been initiated, connect to WebSocket",
                "section_id": section_id
            }
                
        except ValueError as e:
            # Re-raise ValueError as is (for HTTP 404)
            raise
        except Exception as e:
            self.logger.error(f"Error in pickup service: {e}")
            raise Exception(f"Internal server error: {str(e)}")

    
    async def insert_custom_product(
        self, 
        section_id: int, 
        price: Decimal
    ) -> Dict[str, Any]:
        """
        Insert a custom product into database.
        
        Args:
            section_id: Section ID where product will be placed
            price: Product price
            
        Returns:
            Dict with insert result
        """
        from infrastructure.repositories import product_repository
        
        # Check if section already has active product
        has_active = await product_repository.check_section_has_active_product(section_id)
        if has_active:
            log_timeline_event(
                event_type="product_insert",
                event_result="section_occupied",
                message=f"Section {section_id} already has an active product",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": "Section already has an active product",
                "error_code": "100"
            }

        # Insert custom product
        new_product = await product_repository.insert_custom_product(section_id, price)
        if not new_product:
            log_timeline_event(
                event_type="product_insert",
                event_result="failed",
                message=f"Failed to insert custom product in section {section_id}",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": "Failed to insert custom product",
                "error_code": "103"
            }

        log_timeline_event(
            event_type="product_insert",
            event_result="success",
            message=f"Custom product inserted successfully in section {section_id} with price {price}",
            section_id=section_id,
            mode="product"
        )

        return {
            "success": True,
            "message": "Custom product inserted successfully",
            "product": new_product
        }
    
    async def insert_product_with_ean(
        self, 
        section_id: int, 
        ean: str
    ) -> Dict[str, Any]:
        """
        Insert a product with EAN code into database.
        
        Args:
            section_id: Section ID where product will be placed
            ean: EAN code of the product
            
        Returns:
            Dict with insert result
        """
        from infrastructure.repositories import product_repository
        from infrastructure.external_apis import jetveo_client
        
        # Check if section already has active product
        has_active = await product_repository.check_section_has_active_product(section_id)
        if has_active:
            log_timeline_event(
                event_type="product_insert",
                event_result="section_occupied",
                message=f"Section {section_id} already has an active product",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": "Section already has an active product",
                "error_code": "100"
            }

        # Fetch product information from external API
        external_product = await jetveo_client.fetch_product_by_ean(ean)
        if not external_product:
            log_timeline_event(
                event_type="product_insert",
                event_result="ean_not_found",
                message=f"Product with EAN {ean} not found in external API",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": f"Product with EAN {ean} not found in external API",
                "error_code": "111"
            }
        
        # Prepare product data for database
        product_data = {
            "name": external_product.text.cs.name,
            "description": external_product.text.cs.description,
            "price": external_product.price,
            "age_control_required": external_product.ageControl,
            "cover_image": external_product.coverImage
        }
        
        # Insert product into database
        new_product = await product_repository.insert_product_with_ean(section_id, ean, product_data)
        if not new_product:
            log_timeline_event(
                event_type="product_insert",
                event_result="failed",
                message=f"Failed to insert product with EAN {ean} into database",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": "Failed to insert product into database",
                "error_code": "103"
            }

        log_timeline_event(
            event_type="product_insert",
            event_result="success",
            message=f"Product with EAN {ean} inserted successfully in section {section_id}",
            section_id=section_id,
            mode="product"
        )

        return {
            "success": True,
            "message": "Product inserted successfully",
            "product": new_product,
            "external_product": external_product
        }
    
    async def remove_product(self, section_id: int) -> Dict[str, Any]:
        """
        Remove product from section.
        
        Args:
            section_id: Section ID to remove product from
            
        Returns:
            Dict with remove result
        """
        from infrastructure.repositories import product_repository
        
        success = await product_repository.remove_product(section_id)
        if not success:
            log_timeline_event(
                event_type="product_remove",
                event_result="failed",
                message=f"Failed to remove product from section {section_id}",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": "Failed to remove product",
                "error_code": "103"
            }

        log_timeline_event(
            event_type="product_remove",
            event_result="success",
            message=f"Products in section {section_id} have been deactivated",
            section_id=section_id,
            mode="product"
        )

        return {
            "success": True,
            "message": f"Products in section {section_id} have been deactivated"
        }
    
    async def remove_all_products(self) -> Dict[str, Any]:
        """
        Remove all products from all sections.
        
        Returns:
            Dict with remove result
        """
        from infrastructure.repositories import product_repository
        
        success = await product_repository.remove_all_products()
        if not success:
            log_timeline_event(
                event_type="product_remove_all",
                event_result="failed",
                message="Failed to remove all products",
                mode="product"
            )
            return {
                "success": False,
                "message": "Failed to remove all products",
                "error_code": "103"
            }

        log_timeline_event(
            event_type="product_remove_all",
            event_result="success",
            message="All products have been deactivated",
            mode="product"
        )

        return {
            "success": True,
            "message": "All products have been deactivated"
        }
    
    async def list_products(self) -> Dict[str, Any]:
        """
        List all active products.
        
        Returns:
            Dict with products list
        """
        from infrastructure.repositories import product_repository
        
        products = await product_repository.list_products()
        
        return {
            "success": True,
            "products": products,
            "total_count": len(products)
        }
    
    async def list_section_products(self, section_id: int) -> Dict[str, Any]:
        """
        List products in specific section.
        
        Args:
            section_id: Section ID to list products from
            
        Returns:
            Dict with products list
        """
        from infrastructure.repositories import product_repository
        
        products = await product_repository.list_section_products(section_id)
        
        return {
            "success": True,
            "products": products,
            "total_count": len(products)
        }
    
    async def reserve_section(
        self, 
        section_id: int, 
        reservation_pin: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Reserve a section with optional PIN.
        
        Args:
            section_id: Section ID to reserve
            reservation_pin: Optional reservation PIN
            
        Returns:
            Dict with reservation result
        """
        from infrastructure.repositories import product_repository
        
        result = await product_repository.reserve_section(section_id, reservation_pin)
        if not result:
            log_timeline_event(
                event_type="section_reserve",
                event_result="failed",
                message=f"Failed to reserve section {section_id}",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "error_code": "103"
            }

        log_timeline_event(
            event_type="section_reserve",
            event_result="success",
            message=f"Section {section_id} reserved successfully",
            section_id=section_id,
            entered_pin=result.get("reservation_pin"),
            mode="product"
        )

        return {
            "success": True,
            "message": f"Section {section_id} reserved successfully",
            "reservation_pin": result.get("reservation_pin"),
            "product": result
        }
    
    # purchase_product method removed - no longer needed

# Remaining purchase_product method code removed
    
    async def get_hardware_config_for_section(self, section_id: int):
        """
        Get hardware configuration for a section from box_sections table.
        
        Args:
            section_id: The section ID to get configuration for
            
        Returns:
            SectionConfig object with hardware configuration or None if not found
        """
        from managers.session_manager import SectionConfig
        from infrastructure.repositories import product_repository
        
        # This should be moved to a separate hardware repository
        # For now, we'll keep it simple
        import mysql.connector
        from os import getenv
        
        conn = mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
        
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                SELECT section_id, lock_id, tempered, led_section, visible
                FROM box_sections 
                WHERE section_id = %s AND visible = 1
            """, (section_id,))
            
            config = cursor.fetchone()
            
            if not config:
                return None
                
            return SectionConfig(
                section_id=config['section_id'],
                lock_id=int(config['lock_id']) if config['lock_id'] else section_id,
                is_tempered=bool(config['tempered']),
                led_section=config['led_section'] if config['led_section'] else None
            )
            
        except mysql.connector.Error as err:
            self.logger.error(f"Database error getting hardware config: {err}")
            return None
        finally:
            cursor.close()
            conn.close()

    async def update_product_price(
        self, 
        section_id: int, 
        new_price: Decimal
    ) -> Dict[str, Any]:
        """
        Update the price of a product in a specific section.
        
        Args:
            section_id: Section ID containing the product
            new_price: New price for the product
            
        Returns:
            Dict with update result
        """
        from infrastructure.repositories import product_repository
        
        # Validate price
        if new_price < 0:
            log_timeline_event(
                event_type="product_price_update",
                event_result="invalid_price",
                message=f"Attempted to set negative price {new_price} for section {section_id}",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": "Price cannot be negative",
                "error_code": "114"
            }

        result = await product_repository.update_product_price(section_id, new_price)
        if not result:
            log_timeline_event(
                event_type="product_price_update",
                event_result="product_not_found",
                message=f"No active product found in section {section_id}",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": f"No active product found in section {section_id}",
                "error_code": "115"
            }

        log_timeline_event(
            event_type="product_price_update",
            event_result="success",
            message=f"Product price updated from {result.get('old_price', 0)} to {new_price} in section {section_id}",
            section_id=section_id,
            mode="product"
        )

        return {
            "success": True,
            "message": f"Product price updated successfully in section {section_id}",
            "section_id": section_id,
            "old_price": float(result.get("old_price", 0)),
            "new_price": float(new_price),
            "product": result
        }

    async def cancel_reservation(self, section_id: int) -> Dict[str, Any]:
        """
        Cancel a reservation for a section.
        
        Args:
            section_id: Section ID to cancel reservation for
            
        Returns:
            Dict with cancellation result
        """
        from infrastructure.repositories import product_repository
        
        result = await product_repository.cancel_reservation(section_id)
        if not result:
            log_timeline_event(
                event_type="reservation_cancel",
                event_result="not_found",
                message=f"No reserved product found in section {section_id}",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": f"No reserved product found in section {section_id}",
                "error_code": "116"
            }

        log_timeline_event(
            event_type="reservation_cancel",
            event_result="success",
            message=f"Reservation cancelled successfully for section {section_id}",
            section_id=section_id,
            entered_pin=result.get("cancelled_pin"),
            mode="product"
        )

        return {
            "success": True,
            "message": f"Reservation cancelled successfully for section {section_id}",
            "section_id": section_id,
            "cancelled_pin": result.get("cancelled_pin"),
            "product": result
        }

# Global service instance
product_service = ProductService() 