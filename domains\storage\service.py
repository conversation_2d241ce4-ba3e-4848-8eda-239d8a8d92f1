import logging
from typing import Dict, Any
from uuid import uuid4

from managers.session_manager import session_manager, SessionType
from infrastructure.repositories.storage_repository import StorageRepository
from managers.timeline_logger import log_timeline_event

logger = logging.getLogger(__name__)

class StorageService:
    """Service for storage-related business operations"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.repo = StorageRepository()

    async def insert_storage(self, section_id: int, email: str = None) -> Dict[str, Any]:
        """
        Initialize storage insertion process.
        Creates a transaction session and starts the flow.
        """
        self.logger.info(f"Storage insert requested for section_id: {section_id}, email: {email}")

        try:
            # Check if section is available for storage reservation
            availability = self.repo.check_section_availability(section_id)
            if not availability["available"]:
                self.logger.warning(f"Section {section_id} not available: {availability['reason']}")
                return {
                    "success": False,
                    "error": availability["reason"],
                    "message": f"Cannot create reservation: {availability['reason']}"
                }

            # Get category and price from the section
            category_info = self.repo.get_category_by_section(section_id)
            if not category_info:
                return {
                    "success": False,
                    "error": f"No category found for section {section_id}",
                    "message": f"Section {section_id} configuration not found"
                }

            size_category = category_info['size_category']
            price = float(category_info['price'])

            session_id = str(uuid4())

            # Create session in session manager
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.STORAGE_FLOW,
                section_id=section_id,
                amount=price,
                operation="storage_insert",
                size_category=size_category,
                email=email
            )

            if not session:
                raise Exception("Failed to create session")

            self.logger.info(f"Storage insert session created for session {session_id}, section {section_id}")

            # Note: We no longer use flow_coordinator for storage insertion
            # The new approach uses select_sections() from process_manager via websocket handler

            return {
                "success": True,
                "session_id": session_id,
                "status": "session_created",
                "requires_payment": price > 0,
                "amount": price,
                "message": "Storage insertion session created, connect to WebSocket",
                "section_id": section_id
            }

        except ValueError as e:
            return {
                "success": False,
                "error": str(e),
                "message": str(e)
            }
        except Exception as e:
            self.logger.error(f"Error in insert_storage service: {e}")
            return {
                "success": False,
                "error": "Internal server error",
                "message": f"Internal server error: {str(e)}"
            }

    async def get_hardware_config_for_section(self, section_id: int):
        """
        Get hardware configuration for a section from box_sections table.
        """
        from managers.session_manager import SectionConfig
        import mysql.connector
        from os import getenv

        conn = mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
        
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                SELECT section_id, lock_id, tempered, led_section, visible
                FROM box_sections 
                WHERE section_id = %s AND visible = 1
            """, (section_id,))
            
            config = cursor.fetchone()
            
            if not config:
                return None
                
            return SectionConfig(
                section_id=config['section_id'],
                lock_id=int(config['lock_id']) if config['lock_id'] else section_id,
                is_tempered=bool(config['tempered']),
                led_section=config['led_section'] if config['led_section'] else None
            )
            
        except mysql.connector.Error as err:
            self.logger.error(f"Database error getting hardware config: {err}")
            return None
        finally:
            cursor.close()
            conn.close()

    async def pickup_storage(self, reservation_pin: str) -> Dict[str, Any]:
        """
        Initialize storage pickup process.
        Calculates charge based on 25-hour rule:
        - Within 25 hours: No charge (amount = 0)
        - After 25 hours: Charge 63
        """
        self.logger.info(f"Storage pickup requested for pin: {reservation_pin}")
        try:
            reservation = self.repo.find_reservation_by_pin(reservation_pin)
            if not reservation:
                log_timeline_event(
                    event_type="pin_entered",
                    event_result="order_not_found",
                    message="Storage reserved Box not found",
                    entered_pin=reservation_pin,
                    mode="storage"
                )
                return {
                    "success": False,
                    "error": f"No reservation found with pin: {reservation_pin}",
                    "message": f"Invalid reservation PIN: {reservation_pin}"
                }
            
            log_timeline_event(
                    event_type="pin_entered",
                    event_result="section_found",
                    message="Storage reserved Box not found",
                    entered_pin=reservation_pin,
                    section_id=reservation['section_id'],
                    mode="storage"
                )


            # Calculate time difference from created_at
            from datetime import datetime, timedelta
            from os import getenv

            created_at = reservation['created_at']
            current_time = datetime.now()

            # Handle both datetime and string formats
            if isinstance(created_at, str):
                created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))

            time_diff = current_time - created_at
            hours_passed = time_diff.total_seconds() / 3600

            # Get MAX_STORAGE_HOURS from .env file
            max_storage_hours = int(getenv("MAX_STORAGE_HOURS", "25"))  # Default to 25 if not set

            # Get extra charge amount from .env
            extra_charge_amount = float(getenv("EXTRA_CHARGE_AMOUNT", "63"))  # Default to 63 if not set

            # Determine charge based on MAX_STORAGE_HOURS from .env
            if hours_passed <= max_storage_hours:
                extra_charge = 0
                self.logger.info(f"Pickup within {max_storage_hours} hours ({hours_passed:.2f}h), no charge")
            else:
                extra_charge = extra_charge_amount
                self.logger.info(f"Pickup after {max_storage_hours} hours ({hours_passed:.2f}h), charge: {extra_charge}")

            session_id = str(uuid4())

            # Create simple session for storage pickup - no complex flow needed
            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.STORAGE_PICKUP,  # New simple session type
                section_id=reservation['section_id'],
                amount=extra_charge,
                operation="storage_pickup",
                reservation_id=reservation['id']
            )

            if not session:
                raise Exception("Failed to create session")

            return {
                "success": True,
                "session_id": session_id,
                "status": "session_created",
                "requires_payment": extra_charge > 0,
                "amount": extra_charge,
                "message": "Storage pickup session created, connect to WebSocket",
                "section_id": reservation['section_id'],
                "hours_since_creation": round(hours_passed, 2),
                "max_storage_hours": max_storage_hours,
                "extra_charge_amount": extra_charge_amount
            }
        except ValueError as e:
            return {
                "success": False,
                "error": str(e),
                "message": str(e)
            }
        except Exception as e:
            self.logger.error(f"Error in pickup_storage service: {e}")
            return {
                "success": False,
                "error": "Internal server error",
                "message": f"Internal server error: {str(e)}"
            }


# Global service instance
storage_service = StorageService()
