"""
WebSocket handlers for hardware real-time communication.
Routes WebSocket connections to appropriate domain handlers.
"""

from fastapi import WebSocket, WebSocketDisconnect
from .ws_manager import ws_manager
from .session_manager import session_manager, SessionType
import logging

logger = logging.getLogger(__name__)

async def hardware_websocket_endpoint(websocket: WebSocket, session_id: str):
    """
    WebSocket endpoint for hardware sequence communication.
    Routes to appropriate domain handlers based on session type.
    """
    logger.info(f"WebSocket connection attempt for session: {session_id}")

    # Check existence of session
    session = session_manager.get_session(session_id)
    if not session:
        await websocket.close(code=4000, reason="Invalid session ID")
        logger.warning(f"WebSocket closed: Invalid session {session_id}")
        return

    # Accept connection with subprotocol if present
    if "sec-websocket-protocol" in websocket.headers:
        await websocket.accept(subprotocol=websocket.headers["sec-websocket-protocol"])
    else:
        await websocket.accept()

    logger.info(f"WebSocket connected for session: {session_id}")

    try:
        # Route to appropriate domain handler based on session type
        if session.session_type == SessionType.TRANSACTION:
            # Transaction sessions use their own handler
            from .transaction_handler import handle_transaction_websocket
            await handle_transaction_websocket(websocket, session_id)
            
        elif session.session_type == SessionType.PRODUCT_PICKUP:
            # Product pickup sessions - managed by product domain
            from domains.product.websocket_handler import handle_product_pickup_websocket
            await handle_product_pickup_websocket(websocket, session_id)
            
        elif session.session_type == SessionType.STORAGE_FLOW:
            # Storage flow sessions - managed by storage domain
            from domains.storage.websocket_handler import handle_storage_websocket
            await handle_storage_websocket(websocket, session_id)

        elif session.session_type == SessionType.STORAGE_PICKUP:
            # Storage pickup sessions - direct pickup_process usage
            from domains.storage.websocket_handler import handle_storage_pickup_websocket
            await handle_storage_pickup_websocket(websocket, session_id)

        elif session.session_type == SessionType.PRODUCT_PICKUP:
            # Product pickup sessions - direct pickup_process usage
            from domains.product.websocket_handler import handle_product_pickup_websocket
            await handle_product_pickup_websocket(websocket, session_id)

        elif session.session_type == SessionType.ORDER_FLOW:
            # Order flow sessions - managed by order domain
            from domains.order.websocket_handler import handle_order_flow_websocket
            await handle_order_flow_websocket(websocket, session_id)

        elif session.session_type == SessionType.FSM_SEQUENCE:
            # FSM sequences - managed by sequence manager directly
            await _handle_fsm_sequence(websocket, session_id, session)
            
        elif session.session_type == SessionType.BACKEND_SALE:
            # Backend sale - simple hardware operation
            await _handle_backend_hardware_operation(websocket, session_id, session, "insert")
            
        elif session.session_type == SessionType.BACKEND_PICKUP:
            # Backend pickup - simple hardware operation
            await _handle_backend_hardware_operation(websocket, session_id, session, "pickup")
            
        elif session.session_type == SessionType.OPERATOR_FSM:
            # Operator FSM sequences - simplified for operator use
            await _handle_operator_fsm(websocket, session_id, session)
            
        else:
            await websocket.close(code=4000, reason="Invalid session type")
            return

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for session: {session_id}")
    except Exception as e:
        logger.error(f"WebSocket error for session {session_id}: {e}")
    finally:
        # Clean up only for non-flow sessions
        if session and session.session_type not in [SessionType.PRODUCT_PICKUP, SessionType.STORAGE_FLOW]:
            ws_manager.disconnect(session_id)
            logger.info(f"WebSocket cleanup completed for session: {session_id}")

async def _handle_fsm_sequence(websocket: WebSocket, session_id: str, session):
    """Handle FSM sequence session - direct hardware operation"""
    from .sequence_manager import sequence_manager
    
    try:
        # Register connection
        await ws_manager.connect(session_id, websocket)
        
        # Wait for ready signal
        while True:
            data = await websocket.receive_json()
            msg_type = data.get("type")
            
            if msg_type == "ready_for_sequence":
                # Send confirmation
                await ws_manager.send(session_id, {
                    "type": "sequence_starting",
                    "message": "Starting hardware sequence..."
                })
                
                # Start the sequence
                success = await sequence_manager.start_fsm_sequence(
                    session_id=session_id,
                    sections=session.sections or [],
                    pin=session.pin or ""
                )
                
                if not success:
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "Failed to start sequence"
                    })
                    return
                break
            else:
                logger.warning(f"Unknown message type '{msg_type}' for FSM session {session_id}")
                
    except WebSocketDisconnect:
        logger.info(f"FSM WebSocket disconnected for session: {session_id}")

async def _handle_backend_hardware_operation(websocket: WebSocket, session_id: str, session, operation_type: str):
    """Handle simple backend hardware operations (sale/pickup)"""
    from .sequence_manager import sequence_manager
    
    try:
        # Register connection
        await ws_manager.connect(session_id, websocket)
        
        # Wait for ready signal
        while True:
            data = await websocket.receive_json()
            msg_type = data.get("type")
            
            if msg_type == f"ready_for_{operation_type}":
                # Send confirmation
                await ws_manager.send(session_id, {
                    "type": "opening_locker",
                    "message": f"Opening locker for {operation_type}..."
                })
                
                # Start single section sequence
                success = await sequence_manager.start_fsm_sequence(
                    session_id=session_id,
                    sections=session.sections or [],
                    pin=f"backend_{operation_type}"
                )
                
                if not success:
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "Failed to open locker"
                    })
                    return
                else:
                    await ws_manager.send(session_id, {
                        "type": "locker_opened",
                        "message": f"Locker opened. Please {operation_type} the product and close the door."
                    })
                
                break
            else:
                logger.warning(f"Unknown message type '{msg_type}' for Backend {operation_type} session {session_id}")
                
    except WebSocketDisconnect:
        logger.info(f"Backend {operation_type} WebSocket disconnected for session: {session_id}")

async def _handle_operator_fsm(websocket: WebSocket, session_id: str, session):
    """Handle operator FSM sequence - simplified for operator use"""
    from .sequence_manager import sequence_manager
    
    try:
        # Register connection
        await ws_manager.connect(session_id, websocket)
        
        # Send operator session info
        await ws_manager.send(session_id, {
            "type": "operator_session_ready",
            "session_id": session_id,
            "message": "Operator session ready - you can send lists of sections to open"
        })
        
        # Main message loop for operator commands
        while True:
            data = await websocket.receive_json()
            msg_type = data.get("type")
            
            if msg_type == "operator_unlock":
                # Operator wants to unlock specific sections
                sections = data.get("sections", [])
                wait_for_close = data.get("close", False)  # default False for operator
                
                if not sections:
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "No sections to open"
                    })
                    continue
                
                # Get hardware config for each section
                from domains.product.service import product_service
                section_configs = []
                invalid_sections = []
                
                for section_id in sections:
                    config = await product_service.get_hardware_config_for_section(section_id)
                    if config:
                        section_configs.append(config)
                    else:
                        invalid_sections.append(section_id)
                
                if invalid_sections:
                    await ws_manager.send(session_id, {
                        "type": "warning",
                        "message": f"Neplatné schránky: {invalid_sections}",
                        "invalid_sections": invalid_sections
                    })
                
                if section_configs:
                    # Start FSM sequence (same as for customers but with operator logic)
                    success = await sequence_manager.start_fsm_sequence(
                        session_id=session_id,
                        sections=section_configs,
                        pin="operator_service",
                        wait_for_close=wait_for_close
                    )
                    
                    if success:
                        await ws_manager.send(session_id, {
                            "type": "operator_sequence_started",
                            "sections": [s.section_id for s in section_configs],
                            "message": f"Starting opening of {len(section_configs)} sections"
                        })
                    else:
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": "Error starting sequence"
                        })
                else:
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "No valid sections to open"
                    })
                    
            elif msg_type == "operator_stop":
                # Stop current sequence
                stopped = await sequence_manager.stop_sequence(session_id)
                if stopped:
                    await ws_manager.send(session_id, {
                        "type": "operator_sequence_stopped",
                        "message": "Sequence stopped"
                    })
                else:
                    await ws_manager.send(session_id, {
                        "type": "warning",
                        "message": "No active sequence to stop"
                    })
                    
            elif msg_type == "ping":
                # Keep-alive
                await ws_manager.send(session_id, {
                    "type": "pong",
                    "message": "Operator session active"
                })
                
            else:
                logger.warning(f"Unknown operator message type: {msg_type}")
                await ws_manager.send(session_id, {
                    "type": "error",
                    "message": f"Unknown message type: {msg_type}"
                })
                
    except WebSocketDisconnect:
        logger.info(f"Operator FSM WebSocket disconnected for session: {session_id}")
    except Exception as e:
        logger.error(f"Operator FSM WebSocket error for session {session_id}: {e}")
        await ws_manager.send(session_id, {
            "type": "error",
            "message": f"error: {str(e)}"
        })
